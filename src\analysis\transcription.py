import time
from functools import lru_cache
from pathlib import Path
from typing import Any

import librosa
import torch
from pyannote.audio import Pipeline
from pydantic import BaseModel
from rich.console import Console

from src.idempotency import load_and_check_idempotency, save_idempotent_result
from src.utils import get_device, log_step_duration

console = Console()


# --- Pydantic Models for Transcription Analysis ---


class TranscriptionSegment(BaseModel):
    """Represents a single segment of transcribed text."""

    start: float
    end: float
    text: str


class TranscriptionAnalysis(BaseModel):
    """Represents the full transcription analysis result."""

    language: str
    title: str
    segments: list[TranscriptionSegment]


# --- Helper Models ---


class RawWhisperOutput(BaseModel):
    """Wraps the raw dictionary output from the Whisper model for idempotency."""

    model_config = {"extra": "allow"}

    text: str
    chunks: list[dict[str, Any]]
    language: str | None = None


# --- Helper Functions ---


@lru_cache(maxsize=1)
def get_vad_pipeline() -> Pipeline | None:
    """Loads and caches the Pyannote Voice Activity Detection (VAD) pipeline.

    Why is this needed?
    Whisper, while excellent at transcription, can sometimes misidentify the start
    time of speech in audio files that have a long period of silence at the
    beginning. It might report the first word as starting at 0.0 seconds, even
    if the actual speech begins much later. This can desynchronize the transcript
    from the video.

    What does this do?
    This function uses the Pyannote VAD model, a specialized tool for detecting
    voice activity, to find the *actual* start time of speech. The main
    transcription logic then uses this start time as an offset to correct all
    subsequent timestamps from Whisper.

    How to use it:
    This model is gated on Hugging Face and requires authentication.
    1. Accept the user agreement on the model page:
       https://huggingface.co/pyannote/voice-activity-detection
    2. Create a Hugging Face access token with 'read' permissions.
    3. Make the token available as an environment variable named `HUGGING_FACE_HUB_TOKEN`.

    This step is optional and can be enabled with the `--vad` flag.
    """
    console.print("  Loading Pyannote VAD pipeline...")
    device = get_device()
    try:
        # Using a token is required for this gated model.
        # The user should have a huggingface token set up in their environment.
        vad_pipeline = Pipeline.from_pretrained(
            "pyannote/voice-activity-detection",
            use_auth_token=True,  # type: ignore
        ).to(torch.device(device))
        console.print("  [green]VAD pipeline loaded successfully.[/green]")
        return vad_pipeline
    except Exception as e:
        console.print(f"[red]Failed to load Pyannote VAD pipeline: {e}[/red]")
        console.print(
            "[yellow]Please ensure you have a Hugging Face token and have accepted the user agreement for the model.[/yellow]",
        )
        return None


# --- Main Analysis Functions ---


def _convert_chunks_to_segments(
    chunks: list[dict[str, Any]],
) -> list[TranscriptionSegment]:
    """Converts Whisper chunks (now sentence-level) directly to TranscriptionSegments.

    Since we're using sentence-level timestamps from Whisper, we can convert
    chunks more directly without complex word grouping logic.

    Args:
        chunks: A list of sentence-level chunk dictionaries from Whisper.

    Returns:
        A list of TranscriptionSegment objects.
    """
    if not chunks:
        return []

    segments = []

    for chunk in chunks:
        text = chunk.get("text", "").strip()
        if not text:
            continue

        # Handle timestamp safely - it can be a tuple with None values
        timestamp = chunk.get("timestamp")
        if isinstance(timestamp, (list, tuple)) and len(timestamp) >= 2:
            start_time = timestamp[0] if timestamp[0] is not None else 0.0
            end_time = timestamp[1] if timestamp[1] is not None else start_time + 1.0
        elif isinstance(timestamp, (list, tuple)) and len(timestamp) >= 1:
            start_time = timestamp[0] if timestamp[0] is not None else 0.0
            end_time = start_time + 1.0  # Default 1s duration for sentences
        else:
            # Fallback if timestamp format is unexpected
            start_time = 0.0
            end_time = 1.0

        segments.append(
            TranscriptionSegment(
                start=start_time,
                end=end_time,
                text=text,
            ),
        )

    return segments


def _group_words_into_segments(
    word_chunks: list[dict[str, Any]],
    max_gap_s: float = 1.5,  # Gap threshold for sentence detection
    max_duration_s: int = 8,  # Max duration for segments
    min_words_per_segment: int = 3,  # Minimum words per segment
) -> list[TranscriptionSegment]:
    """Groups word-level chunks from Whisper into larger, more readable segments.

    Why is this needed?
    To achieve the goal of short, accurately timestamped segments, we instruct
    Whisper to return individual words with their timestamps (`return_timestamps="word"`).
    However, presenting each word as a separate event in the timeline would be
    overwhelming and not very useful. This function takes the raw stream of
    word-level timestamps and intelligently groups them into coherent, sentence-like
    segments based on natural pauses (silence gaps) and a maximum desired length.
    This restores the desired output format of short, captioned segments.

    Args:
        word_chunks: A list of word-level chunk dictionaries from Whisper.
        max_gap_s: The maximum silence gap between words before starting a new segment.
        max_duration_s: The maximum duration of a single segment before forcing a split.

    Returns:
        A list of merged TranscriptionSegment objects.
    """
    if not word_chunks:
        return []

    segments = []
    current_segment_text = ""

    # Handle the first chunk's timestamp safely
    first_timestamp = word_chunks[0]["timestamp"]
    if isinstance(first_timestamp, (list, tuple)) and len(first_timestamp) >= 1:
        segment_start_time = first_timestamp[0] if first_timestamp[0] is not None else 0.0
    else:
        segment_start_time = 0.0

    last_word_end_time = segment_start_time

    for chunk in word_chunks:
        word_text = chunk["text"]
        # Whisper includes spacing in the text, so we just concatenate
        if not word_text:
            continue

        # Handle timestamp safely - it can be a tuple with None values
        timestamp = chunk["timestamp"]
        if isinstance(timestamp, (list, tuple)) and len(timestamp) >= 2:
            word_start = timestamp[0] if timestamp[0] is not None else last_word_end_time
            word_end = timestamp[1] if timestamp[1] is not None else word_start + 0.1  # Default 0.1s duration
        elif isinstance(timestamp, (list, tuple)) and len(timestamp) >= 1:
            word_start = timestamp[0] if timestamp[0] is not None else last_word_end_time
            word_end = word_start + 0.1  # Default 0.1s duration
        else:
            # Fallback if timestamp format is unexpected
            word_start = last_word_end_time
            word_end = word_start + 0.1

        # Check for sentence boundaries and segment length
        # Look for sentence-ending punctuation in the previous text
        has_sentence_end = current_segment_text and any(current_segment_text.rstrip().endswith(punct) for punct in [".", "!", "?"])

        # Start a new segment if:
        # 1. There's a pause after sentence-ending punctuation (prioritize sentence boundaries)
        # 2. There's a very long pause (regardless of punctuation)
        # 3. The segment is getting too long
        is_new_segment = current_segment_text and (
            (has_sentence_end and word_start - last_word_end_time > 0.2)  # Short pause after sentence
            or (word_start - last_word_end_time > max_gap_s)  # Long pause
            or (word_end - segment_start_time > max_duration_s)  # Too long
        )

        if is_new_segment:
            # Finalize the previous segment
            segments.append(
                TranscriptionSegment(
                    start=segment_start_time,
                    end=last_word_end_time,
                    text=current_segment_text.strip(),
                ),
            )
            # Reset for the new segment
            current_segment_text = ""

        # Append word to current segment
        if not current_segment_text:
            segment_start_time = word_start  # Ensure start time is from the first word
        current_segment_text += word_text
        last_word_end_time = word_end

    # Add the final segment
    if current_segment_text:
        segments.append(
            TranscriptionSegment(
                start=segment_start_time,
                end=last_word_end_time,
                text=current_segment_text.strip(),
            ),
        )

    return segments


def analyze_transcription(
    speech_path: Path,
    output_path: Path,
    transcription_pipeline: Any,
    video_title: str = "Untitled Video",
    force: bool = False,
    language: str | None = None,
    prompt: str | None = None,
    use_vad: bool = True,  # VAD enabled by default to fix start time issues
) -> TranscriptionAnalysis | None:
    """Transcribes an audio file using Whisper and saves the raw and processed results.

    This function implements a two-step, idempotent process:
    1.  **Raw Transcription:** Runs the Whisper model on the audio, saving the
        unmodified output to `..._raw.json`. This step is skipped if the
        raw output already exists and inputs/parameters are unchanged.
    2.  **Processing:** Loads the raw transcription, applies timestamp corrections
        (like VAD), and saves the final, structured result to the specified
        `output_path`. This step is also idempotent.

    Args:
        speech_path: Path to the audio file containing speech.
        output_path: Path to save the final processed JSON analysis result.
        transcription_pipeline: The pre-loaded Whisper pipeline object.
        force: If True, re-run all analysis steps even if cached results exist.
        language: The language of the audio. If None, it will be auto-detected.
        prompt: An optional initial prompt to guide the transcription.
        use_vad: If True, use Voice Activity Detection (VAD) to find the actual
                 start of speech and correct Whisper's timestamps. This is useful
                 for audio with long leading silences.

    Returns:
        A TranscriptionAnalysis object, or None on error.
    """
    console.print(f"Analyzing transcription for [cyan]{speech_path.name}[/cyan]...")
    overall_start_time = time.time()

    # --- Path for raw model output ---
    raw_output_path = output_path.with_name(f"{output_path.stem}_raw.json")

    # --- Step 1: Get Raw Transcription from Whisper (Idempotent) ---
    raw_params = {
        "language": language,
        "prompt": prompt,
        "model": transcription_pipeline.model.name_or_path,
    }
    raw_input_files = [speech_path]

    raw_transcription_obj: RawWhisperOutput | None = load_and_check_idempotency(
        output_path=raw_output_path,
        model_class=RawWhisperOutput,
        input_files=raw_input_files,
        params=raw_params,
        force=force,
    )

    if raw_transcription_obj:
        console.print(f"Raw transcription is up to date for [cyan]{speech_path.name}[/cyan].")
    else:
        console.print(f"Running Whisper model on [cyan]{speech_path.name}[/cyan]...")
        analysis_start_time = time.time()
        try:
            # Use the simple pipeline approach which works reliably
            # Load audio file
            audio_input, sample_rate = librosa.load(speech_path, sr=16000)

            # Set up generation parameters to reduce warnings and improve performance
            generate_kwargs = {
                "task": "transcribe",
                "language": language or "en",  # Specify language to avoid auto-detection overhead
            }
            if prompt:
                generate_kwargs["initial_prompt"] = prompt

            # Use the pipeline directly with word-level timestamps for precise control
            # We'll group words into sentences using improved logic
            pipeline_output = transcription_pipeline(
                audio_input,
                return_timestamps="word",  # Use word-level for precise sentence detection
                generate_kwargs=generate_kwargs,
            )



            # The language is not automatically added in this path, so add it back.
            if "language" not in pipeline_output or not pipeline_output["language"]:
                pipeline_output["language"] = language or "en"

            raw_transcription_obj = RawWhisperOutput.model_validate(pipeline_output)

        except Exception as e:
            console.print(f"[red]Error occurred during transcription: {e}[/red]")

            # Also print to console, just in case it's visible.
            console.print("-" * 80)
            console.print("[bold red]FATAL ERROR in analyze_transcription[/bold red]")
            console.print(f"[bold red]Exception Type: {type(e)}[/bold red]")
            console.print(e)
            console.print("-" * 80)
            return None
        log_step_duration(analysis_start_time, "Transcription inference")

        # Save the raw result
        save_idempotent_result(
            output_path=raw_output_path,
            result=raw_transcription_obj,
            input_files=raw_input_files,
            params=raw_params,
        )
        console.print(f"Raw transcription saved to [green]{raw_output_path.name}[/green]")

    # This is the crucial check to prevent None access later
    if not raw_transcription_obj:
        console.print("[red]Could not load or generate raw transcription. Aborting.[/red]")
        return None

    # --- Step 2: Process Raw Transcription (Idempotent) ---
    processing_params = {"use_vad": use_vad}
    processing_input_files = [raw_output_path]

    cached_processed_result = load_and_check_idempotency(
        output_path=output_path,
        model_class=TranscriptionAnalysis,
        input_files=processing_input_files,
        params=processing_params,
        force=force,  # Force processing if raw was forced
    )

    if cached_processed_result:
        console.print(f"Processed transcription is up to date for [cyan]{speech_path.name}[/cyan].")
        log_step_duration(overall_start_time, "Total transcription analysis")
        return cached_processed_result

    # VAD to find speech start (optional)
    start_offset = 0.0
    vad_start_end_timestamps = None
    if use_vad and speech_path.stat().st_size > 0:
        vad_pipeline = get_vad_pipeline()
        if vad_pipeline:
            try:
                vad_start_time = time.time()
                console.print("  Running VAD to find speech start...")
                speech_regions = vad_pipeline(str(speech_path))
                if not speech_regions.is_empty():
                    first_speech_segment = speech_regions.timeline().support()[0]
                    vad_start_end_timestamps = (first_speech_segment.start, first_speech_segment.end)
                    console.print(f"  VAD detected speech start at {vad_start_end_timestamps[0]:.2f}s.")
                else:
                    console.print("  VAD did not detect any speech.")
                log_step_duration(vad_start_time, "VAD analysis")
            except Exception as e:
                console.print(f"[yellow]VAD analysis failed: {e}. Proceeding without VAD.[/yellow]")

    # Process word-level chunks into merged segments
    console.print("Grouping word-level timestamps into segments...")
    processed_transcription = _save_transcription_results(
        raw_transcription_obj,
        output_path,
        raw_output_path,
        processing_input_files,
        processing_params,
        video_title,
        use_vad,
        vad_start_end_timestamps,
    )

    log_step_duration(overall_start_time, "Total transcription analysis")
    return processed_transcription


def _save_transcription_results(
    raw_transcription_obj: RawWhisperOutput,
    output_path: Path,
    raw_output_path: Path,
    processing_input_files: list[Path],
    processing_params: dict,
    video_title: str,
    vad: bool,
    vad_start_end_timestamps: tuple[float, float] | None = None,
) -> TranscriptionAnalysis | None:
    """Saves the raw and processed transcription results.
    Groups words into segments and applies VAD offsets if necessary.
    """
    # Save the raw model output first, with its own idempotency metadata
    save_idempotent_result(
        output_path=raw_output_path,
        result=raw_transcription_obj,
        input_files=processing_input_files,
        params=processing_params,
    )

    if not raw_transcription_obj.chunks:
        console.print("[yellow]Whisper returned no timestamped chunks. Skipping segmentation.[/yellow]")
        # Create an empty but valid result to save
        processed_transcription = TranscriptionAnalysis(
            language=raw_transcription_obj.language or "unknown",
            title=video_title,
            segments=[],
        )
        save_idempotent_result(
            output_path=output_path,
            result=processed_transcription,
            input_files=processing_input_files,
            params=processing_params,
        )
        return processed_transcription

    # Convert word-level chunks to sentence-level segments
    segments = _group_words_into_segments(raw_transcription_obj.chunks)

    # If VAD was used, adjust the timestamps of the final segments
    if vad and vad_start_end_timestamps:
        start_offset = vad_start_end_timestamps[0]
        console.print(f"Applying VAD start offset of [cyan]{start_offset:.2f}s[/cyan] to all segments.")
        for seg in segments:
            seg.start += start_offset
            seg.end += start_offset

    # Create and save the final processed result
    processed_transcription = TranscriptionAnalysis(
        language=raw_transcription_obj.language or "unknown",
        title=video_title,
        segments=segments,
    )

    # Save the clean transcription data (without idempotency wrapper)
    with output_path.open("w", encoding="utf-8") as f:
        f.write(processed_transcription.model_dump_json(indent=2))

    # Save the idempotency metadata to a separate file
    metadata_path = output_path.with_name("_transcript_metadata.json")
    save_idempotent_result(
        output_path=metadata_path,
        result=processed_transcription,
        input_files=processing_input_files,
        params=processing_params,
    )

    return processed_transcription
