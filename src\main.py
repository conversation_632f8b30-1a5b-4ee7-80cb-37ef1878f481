from __future__ import annotations

import re
import time
from pathlib import Path
from typing import Annotated, Any

import torch
import typer
from rich.console import Console
from rich.table import Table
from transformers.pipelines import pipeline

from .processing import process_videos
from .utils import human_readable_duration
from .youtube import get_top_videos

# --- Typer App ---

console = Console()
app = typer.Typer()


@app.command()
def analyze_channel(
    channel_url: Annotated[
        str,
        typer.Argument(..., help="The URL of the YouTube channel."),
    ],
    top_n: Annotated[
        int,
        typer.Option(
            ...,
            "--top-n",
            help="The number of top videos to process.",
        ),
    ],
    run_transcription: Annotated[
        bool,
        typer.Option(
            "--transcription/--no-transcription",
            help="Enable or disable transcription analysis.",
        ),
    ] = True,
    run_sound_effects: Annotated[
        bool,
        typer.Option(
            "--sound-effects/--no-sound-effects",
            help="Enable or disable sound effect analysis.",
        ),
    ] = False,
    run_music_analysis: Annotated[
        bool,
        typer.Option(
            "--music/--no-music",
            help="Enable or disable music analysis.",
        ),
    ] = False,
    force_separation: Annotated[
        bool,
        typer.Option("--force-separation", help="Force re-running audio separation."),
    ] = False,
    force_transcription: Annotated[
        bool,
        typer.Option("--force-transcription", help="Force re-running transcription."),
    ] = False,
    force_music: Annotated[
        bool,
        typer.Option("--force-music", help="Force re-running music analysis."),
    ] = False,
    force_sfx: Annotated[
        bool,
        typer.Option("--force-sfx", help="Force re-running sound effect analysis."),
    ] = False,
    force_timeline: Annotated[
        bool,
        typer.Option("--force-timeline", help="Force re-generating the timeline."),
    ] = False,
    force_frames: Annotated[
        bool,
        typer.Option("--force-frames", help="Force re-running frame analysis."),
    ] = False,
    use_vad: Annotated[
        bool,
        typer.Option(
            "--vad/--no-vad",
            help="Enable or disable Voice Activity Detection (VAD) to correct transcription start times.",
        ),
    ] = False,
    use_youtube_transcript: Annotated[
        bool,
        typer.Option(
            "--youtube-transcript/--whisper-transcript",
            help="Use YouTube's official transcript (default) or transcribe with Whisper.",
        ),
    ] = True,
) -> None:
    """Analyzes a YouTube channel, downloads and processes the top N videos."""
    console.print(f"Analyzing channel: {channel_url}")
    channel_title, videos = get_top_videos(channel_url, top_n)

    if not videos:
        console.print("[red]No videos found or an error occurred.[/red]")
        raise typer.Exit

    if not channel_title:
        channel_title = "Unknown Channel"
        console.print("[yellow]Could not determine channel title.[/yellow]")

    # Display top videos in a table
    table = Table(title=f"Top {top_n} Videos for '{channel_title}'")
    table.add_column("Title", style="cyan", no_wrap=True)
    table.add_column("ID", style="magenta")
    table.add_column("Views", justify="right", style="green")

    for video in videos:
        table.add_row(
            video.title or "N/A",
            video.id or "N/A",
            f"{video.view_count or 0:,}",
        )
    console.print(table)

    # Setup transcription pipeline
    transcription_pipeline: Any = None
    if videos and run_transcription:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        # Use float16 for memory efficiency on GPU
        torch_dtype = torch.float16
        model_id = "openai/whisper-large-v3"

        console.print(f"Attempting to load transcription model on device: {device}")
        console.print(f"Loading transcription pipeline: {model_id}...")
        try:
            transcription_pipeline = pipeline(
                "automatic-speech-recognition",
                model=model_id,
                torch_dtype=torch_dtype,
                device=device,
            )
            console.print("[green]Transcription pipeline loaded successfully.[/green]")
        except Exception as e:
            console.print(f"[red]Error loading transcription pipeline: {e}[/red]")
            console.print("[yellow]Proceeding without transcription.[/yellow]")
    elif not run_transcription:
        console.print("[yellow]Transcription analysis disabled by user.[/yellow]")

    # Process videos
    safe_channel_title = re.sub(r'[\\/*?"<>|]', "", channel_title)
    output_dir = Path("output") / safe_channel_title.replace(" ", "_").lower()
    overall_start_time = time.time()
    process_videos(
        videos,
        output_dir,
        transcription_pipeline,
        run_transcription=run_transcription,
        run_sound_effects=run_sound_effects,
        run_music_analysis=run_music_analysis,
        force_separation=force_separation,
        force_transcription=force_transcription,
        force_music=force_music,
        force_sfx=force_sfx,
        force_timeline=force_timeline,
        force_frames=force_frames,
        use_vad=use_vad,
        use_youtube_transcript=use_youtube_transcript,
    )
    overall_duration = time.time() - overall_start_time

    console.print(
        f"[bold green]Analysis complete for {channel_title}. "
        f"Total processing time: {human_readable_duration(overall_duration)}[/bold green]",
    )


if __name__ == "__main__":
    app()
