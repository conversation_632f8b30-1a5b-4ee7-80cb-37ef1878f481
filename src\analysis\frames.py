import json
import re
import time
from functools import lru_cache
from pathlib import Path

import cv2
import imagehash
import numpy as np
import torch
from PIL import Image
from pydantic import BaseModel
from rich.console import Console
from rich.progress import track
from sentence_transformers import SentenceTransformer
from transformers import AutoProcessor, LlavaForConditionalGeneration

from src.idempotency import load_and_check_idempotency, save_idempotent_result
from src.utils import get_device, log_step_duration

console = Console()


# --- Pydantic Models for Frame Analysis ---


class UniqueFrame(BaseModel):
    """Represents a single unique frame selected for description."""

    frame_path: str
    sharpness: float
    timestamp: float


class FrameAnalysisEvent(BaseModel):
    """Represents a single unique frame event in the timeline."""

    timestamp: float
    image_hash: str


class UniqueImage(BaseModel):
    """Represents a unique image identified from the video frames."""

    description: str
    path: str


class FrameAnalysis(BaseModel):
    """Represents the analysis results for video frames."""

    unique_images: dict[str, UniqueImage]
    events: list[FrameAnalysisEvent]


class UniqueFramesAnalysis(BaseModel):
    """The result of the unique frame selection analysis."""

    unique_frames: list[UniqueFrame]


class DescribedFrame(BaseModel):
    """Represents a single frame with its generated description."""

    frame_path: str
    description: str
    timestamp: float


class FrameDescriptionAnalysis(BaseModel):
    """The result of the frame description analysis step."""

    described_frames: list[DescribedFrame]


# --- Frame Analysis Helpers ---


@lru_cache(maxsize=1)
def get_semantic_similarity_model() -> SentenceTransformer | None:
    """Gets the semantic similarity model for comparing frame embeddings."""
    console.print("  Loading semantic similarity model...")
    try:
        # Use a lightweight CLIP model for image embeddings
        model = SentenceTransformer("clip-ViT-B-32")
        device = get_device()
        model = model.to(device)
        console.print("  [green]Semantic similarity model loaded successfully.[/green]")
        return model
    except Exception as e:
        console.print(f"[red]Failed to load semantic similarity model: {e}[/red]")
        return None


@lru_cache(maxsize=1)
def get_llava_pipeline() -> tuple[LlavaForConditionalGeneration | None, AutoProcessor | None, str]:
    """Gets the Llava model and processor, caching it using lru_cache."""
    console.print("  Loading Llava pipeline...")
    device = get_device()
    # This is a large model, so we handle potential memory issues
    model_id = "llava-hf/llava-1.5-7b-hf"
    pipeline = None
    processor = None
    try:
        processor = AutoProcessor.from_pretrained(model_id, use_fast=True)
        model = LlavaForConditionalGeneration.from_pretrained(
            model_id,
            torch_dtype=torch.float16,
            low_cpu_mem_usage=True,
        )
        pipeline = model.to(device)
    except Exception as e:
        console.print(f"[red]Failed to load Llava model '{model_id}': {e}[/red]")

    return pipeline, processor, device


# --- Main Analysis Functions ---


def analyze_frame_descriptions(
    unique_frames_path: Path,
    output_path: Path,
    prompt: str = "Describe this image in a concise sentence.",
    force: bool = False,
) -> FrameDescriptionAnalysis | None:
    """Generates descriptions for unique frames using Llava with resumable progress.

    This function is interruptible and resumable. It saves progress incrementally,
    so if interrupted, re-running will continue from where it left off.

    Args:
        unique_frames_path: Path to the unique frames analysis file.
        output_path: Path to save the description analysis file.
        prompt: The prompt to use for the Llava model.
        force: If True, re-run the analysis even if a cached result exists.
    """
    console.print(f"Analyzing frame descriptions for [cyan]{unique_frames_path.name}[/cyan]...")
    overall_start_time = time.time()

    # 1. Idempotency check
    params = {"prompt": prompt, "model": "llava-hf/llava-1.5-7b-hf"}
    input_files = [unique_frames_path]

    cached_result = load_and_check_idempotency(
        output_path=output_path,
        model_class=FrameDescriptionAnalysis,
        input_files=input_files,
        params=params,
        force=force,
    )
    if cached_result:
        return cached_result

    # 2. Load unique frames from the previous step
    try:
        data = json.loads(unique_frames_path.read_text(encoding="utf-8"))
        unique_frames_result = UniqueFramesAnalysis.model_validate(data["result"])
    except (KeyError, json.JSONDecodeError, Exception) as e:
        console.print(f"[red]Could not load or parse unique frames file {unique_frames_path}: {e}[/red]")
        return None

    # 3. Load model
    model_load_start = time.time()
    llava_pipeline, llava_processor, device = get_llava_pipeline()
    if not llava_pipeline or not llava_processor:
        console.print("[red]Llava pipeline not available. Skipping description.[/red]")
        return None
    log_step_duration(model_load_start, "Llava model loading")

    # 4. Generate descriptions with resumable progress
    description_start = time.time()
    progress_path = output_path.with_suffix(".progress.json")

    # Load existing progress if available
    described_frames = []
    processed_frame_paths = set()

    if progress_path.exists() and not force:
        try:
            progress_data = json.loads(progress_path.read_text(encoding="utf-8"))
            described_frames = [DescribedFrame.model_validate(frame) for frame in progress_data.get("described_frames", [])]
            processed_frame_paths = set(frame.frame_path for frame in described_frames)
            console.print(f"  Resuming from {len(described_frames)} previously described frames")
        except Exception as e:
            console.print(f"[yellow]Could not load progress file: {e}. Starting fresh.[/yellow]")
            described_frames = []
            processed_frame_paths = set()

    # Process remaining frames
    remaining_frames = [frame for frame in unique_frames_result.unique_frames if frame.frame_path not in processed_frame_paths]

    if remaining_frames:
        console.print(f"  Processing {len(remaining_frames)} remaining frames...")

        for i, frame in enumerate(track(remaining_frames, description="Describing frames...")):
            try:
                image = Image.open(frame.frame_path)
                full_prompt = f"USER: <image>\n{prompt}\nASSISTANT:"
                inputs = llava_processor(
                    text=full_prompt,
                    images=image,
                    return_tensors="pt",
                ).to(device)

                generate_ids = llava_pipeline.generate(**inputs, max_new_tokens=75)
                output = llava_processor.batch_decode(
                    generate_ids,
                    skip_special_tokens=True,
                    clean_up_tokenization_spaces=False,
                )[0]

                # Clean up the output, removing the prompt
                assistant_response = output.split("ASSISTANT:")[-1].strip()

                described_frames.append(
                    DescribedFrame(
                        frame_path=frame.frame_path,
                        description=assistant_response,
                        timestamp=frame.timestamp,
                    ),
                )

                # Save progress every 10 frames
                if (i + 1) % 10 == 0:
                    progress_data = {
                        "described_frames": [frame.model_dump() for frame in described_frames],
                        "total_frames": len(unique_frames_result.unique_frames),
                        "completed_frames": len(described_frames),
                    }
                    progress_path.write_text(json.dumps(progress_data, indent=2), encoding="utf-8")
                    console.print(f"  Progress saved: {len(described_frames)}/{len(unique_frames_result.unique_frames)} frames")

            except Exception as e:
                console.print(
                    f"[yellow]Could not describe frame {frame.frame_path}: {e}[/yellow]",
                )
                continue

    # Clean up progress file on completion
    if progress_path.exists():
        progress_path.unlink()

    log_step_duration(description_start, "Frame description")

    # 5. Create and save result
    result = FrameDescriptionAnalysis(described_frames=described_frames)
    save_idempotent_result(
        output_path=output_path,
        result=result,
        input_files=input_files,
        params=params,
    )
    log_step_duration(overall_start_time, "Total frame description analysis")
    return result


def analyze_unique_frames(
    frame_paths: list[Path],
    output_path: Path,
    fps: float,
    hash_size: int = 16,
    sharpness_threshold: float = 20.0,
    semantic_similarity_threshold: float = 0.98,
    force: bool = False,
) -> UniqueFramesAnalysis | None:
    """Analyzes a list of frames to find a unique set using two-pass filtering.

    This function is idempotent. It performs several steps:
    1.  Checks for a cached result. If found and inputs/params are unchanged, returns it.
    2.  Filters out pure black or near-black frames.
    3.  Pass 1: Calculates a perceptual hash (phash) for each remaining frame and groups by identical hash.
    4.  Pass 2: For frames with different hashes, uses semantic similarity to filter out very similar frames.
    5.  For each group, it selects the sharpest frame as the representative.
    6.  Saves the result with idempotency info.

    Args:
        frame_paths: A list of paths to the frame images.
        output_path: The path to save the JSON analysis result.
        fps: The framerate of the video, used for accurate timestamp calculation.
        hash_size: The size of the perceptual hash.
        sharpness_threshold: The minimum sharpness score to consider.
        semantic_similarity_threshold: Cosine similarity threshold for semantic filtering (0.85 = very similar).
        force: If True, re-run analysis even if a cached result exists.

    Returns:
        A UniqueFramesAnalysis object containing the list of unique frames, or None on error.
    """
    console.print(f"Analyzing {len(frame_paths)} frames for unique content...")
    overall_start_time = time.time()

    # 1. Idempotency Check
    params = {
        "hash_size": hash_size,
        "sharpness_threshold": sharpness_threshold,
        "semantic_similarity_threshold": semantic_similarity_threshold,
        "fps": fps,
    }
    cached_result = load_and_check_idempotency(
        output_path=output_path,
        model_class=UniqueFramesAnalysis,
        input_files=frame_paths,
        params=params,
        force=force,
    )
    if cached_result:
        console.print(f"Unique frames are up to date for [cyan]{output_path.name}[/cyan]. Skipping.")
        return cached_result

    # 2. Process frames: calculate hash and sharpness
    processing_start_time = time.time()
    processed_frames = []
    for frame_path in track(frame_paths, description="Processing frames..."):
        try:
            # Open with Pillow for hashing
            image = Image.open(frame_path)
            # Open with OpenCV for sharpness calculation
            cv_image = cv2.imread(str(frame_path))

            if cv_image is None:
                continue

            # Filter out black frames
            if np.max(cv_image) < 10:  # Heuristic for black frames
                continue

            # Calculate sharpness
            sharpness = cv2.Laplacian(cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY), cv2.CV_64F).var()

            # Calculate perceptual hash
            image_hash = imagehash.phash(image, hash_size=hash_size)

            # Extract timestamp from filename
            # Since frames are extracted at 1 FPS, frame number directly corresponds to seconds
            match = re.search(r"frame_(\d+)\.jpg$", frame_path.name)
            if match:
                frame_number = int(match.group(1))
                timestamp = frame_number - 1  # frame_000001.jpg is at 0 seconds, frame_000002.jpg is at 1 second, etc.
                processed_frames.append(
                    {
                        "path": frame_path,
                        "hash": image_hash,
                        "sharpness": sharpness,
                        "timestamp": timestamp,
                    },
                )
        except Exception as e:
            console.print(f"[yellow]Could not process frame {frame_path}: {e}[/yellow]")
            continue
    log_step_duration(processing_start_time, "Frame processing (hash and sharpness)")

    if not processed_frames:
        console.print("[yellow]No suitable frames found after filtering.[/yellow]")
        result = UniqueFramesAnalysis(unique_frames=[])
        save_idempotent_result(
            output_path=output_path,
            result=result,
            input_files=frame_paths,
            params=params,
        )
        return result

    # 3. Pass 1: Group frames by identical hash
    grouping_start_time = time.time()
    frame_groups: dict[imagehash.ImageHash, list[dict]] = {}
    for frame in processed_frames:
        frame_hash = frame["hash"]
        if frame_hash not in frame_groups:
            frame_groups[frame_hash] = []
        frame_groups[frame_hash].append(frame)

    # Select the sharpest frame from each hash group
    hash_filtered_frames = []
    for frame_group in frame_groups.values():
        if frame_group:
            sharpest_frame = max(frame_group, key=lambda x: x["sharpness"])
            hash_filtered_frames.append(sharpest_frame)

    log_step_duration(grouping_start_time, f"Pass 1: Hash filtering ({len(processed_frames)} -> {len(hash_filtered_frames)} frames)")

    # 4. Pass 2: Semantic similarity filtering (temporarily disabled)
    semantic_start_time = time.time()
    # semantic_model = get_semantic_similarity_model()
    semantic_model = None  # Temporarily disable semantic filtering

    if False and semantic_model and len(hash_filtered_frames) > 1:  # Disabled
        console.print(f"  Running semantic similarity filtering on {len(hash_filtered_frames)} frames...")

        # Generate embeddings for all frames
        embeddings = []
        embedding_errors = 0
        for frame in track(hash_filtered_frames, description="Generating embeddings..."):
            try:
                # CLIP models in sentence-transformers expect image paths as strings
                embedding = semantic_model.encode(str(frame["path"]))
                embeddings.append(embedding)
            except Exception as e:
                console.print(f"[yellow]Could not generate embedding for {frame['path']}: {e}[/yellow]")
                embeddings.append(None)
                embedding_errors += 1

        # If too many embedding errors, skip semantic filtering
        if embedding_errors > len(hash_filtered_frames) * 0.5:
            console.print(f"[yellow]Too many embedding errors ({embedding_errors}/{len(hash_filtered_frames)}), skipping semantic filtering[/yellow]")
            unique_frames_data = hash_filtered_frames
        else:
            # Filter out semantically similar frames
            unique_frames_data = []
            for i, frame in enumerate(hash_filtered_frames):
                if embeddings[i] is None:
                    continue

                is_unique = True
                for j, existing_frame in enumerate(unique_frames_data):
                    if embeddings[existing_frame["original_index"]] is None:
                        continue

                    # Calculate cosine similarity
                    similarity = np.dot(embeddings[i], embeddings[existing_frame["original_index"]]) / (
                        np.linalg.norm(embeddings[i]) * np.linalg.norm(embeddings[existing_frame["original_index"]])
                    )

                    if similarity > semantic_similarity_threshold:
                        is_unique = False
                        # Keep the sharper frame
                        if frame["sharpness"] > existing_frame["sharpness"]:
                            unique_frames_data[j] = {**frame, "original_index": i}
                        break

                if is_unique:
                    unique_frames_data.append({**frame, "original_index": i})

            console.print(f"  Semantic filtering: {len(hash_filtered_frames)} -> {len(unique_frames_data)} frames")
    else:
        # No semantic filtering, use hash-filtered frames
        unique_frames_data = hash_filtered_frames

    log_step_duration(semantic_start_time, "Pass 2: Semantic similarity filtering")

    # 5. Create final unique frames list
    selection_start_time = time.time()
    unique_frames = []
    for frame_data in unique_frames_data:
        unique_frames.append(
            UniqueFrame(
                frame_path=str(frame_data["path"]),
                sharpness=frame_data["sharpness"],
                timestamp=frame_data["timestamp"],
            ),
        )

    # Sort frames by timestamp for consistent output
    unique_frames.sort(key=lambda x: x.timestamp)
    log_step_duration(selection_start_time, "Final frame selection")

    # 5. Save the result
    result = UniqueFramesAnalysis(unique_frames=unique_frames)
    save_idempotent_result(
        output_path=output_path,
        result=result,
        input_files=frame_paths,
        params=params,
    )

    console.print(
        f"[green]Identified {len(unique_frames)} unique frames. Result saved to {output_path.name}[/green]",
    )
    log_step_duration(overall_start_time, "Total unique frame analysis")
    return result
