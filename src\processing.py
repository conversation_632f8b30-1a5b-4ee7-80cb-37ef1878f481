import json
import re
import subprocess
import time
from datetime import date
from pathlib import Path
from typing import Any

import yt_dlp
from pydantic import BaseModel
from rich.console import Console

from src.analysis.frames import (
    analyze_frame_descriptions,
    analyze_unique_frames,
)
from src.analysis.music import MusicAnalysis, analyze_music
from src.analysis.sound import analyze_sound_effects
from src.analysis.transcription import (
    TranscriptionAnalysis,
    TranscriptionSegment,
    analyze_transcription,
)
from src.idempotency import load_and_check_idempotency, save_idempotent_result
from src.separation import separate_audio_demucs
from src.utils import log_step_duration
from src.youtube import VideoInfo

console = Console()


class TimelineEvent(BaseModel):
    """Represents a single event on the unified timeline."""

    type: str
    start_time: float
    end_time: float
    text: str
    image_path: str | None = None


class VideoMetadataTimeline(BaseModel):
    """Represents the metadata of the video in the timeline."""

    title: str | None
    author: str | None
    url: str
    id: str
    view_count: int | None
    upload_date: date | None


class Timeline(BaseModel):
    """The root model for the final timeline JSON structure."""

    video_metadata: VideoMetadataTimeline
    analysis_timeline: list[TimelineEvent]


def extract_frames(video_path: Path, frames_dir: Path) -> float:
    """Extracts frames from a video file. Returns the video's framerate, or 0.0 on failure."""
    # 1. Probe video for FPS using a direct ffprobe call
    try:
        ffprobe_cmd = [
            "ffprobe",
            "-v",
            "error",
            "-select_streams",
            "v:0",
            "-show_entries",
            "stream=r_frame_rate",
            "-of",
            "default=noprint_wrappers=1:nokey=1",
            str(video_path),
        ]
        result = subprocess.run(
            ffprobe_cmd,
            capture_output=True,
            text=True,
            check=True,
        )
        r_frame_rate = result.stdout.strip()
        num, den = map(int, r_frame_rate.split("/"))
        fps = num / den
    except (subprocess.CalledProcessError, ValueError, FileNotFoundError) as e:
        console.print(f"[red]Error probing video file {video_path.name} with ffprobe: {e}[/red]")
        console.print("[yellow]Please ensure ffmpeg and ffprobe are installed and in your system's PATH.[/yellow]")
        return 0.0

    # 2. Check if frames already exist
    if frames_dir.exists() and any(frames_dir.iterdir()):
        console.print(f"Frames already exist for {video_path.name}, skipping extraction.")
        return fps

    # 3. Extract frames using a direct ffmpeg call
    frames_dir.mkdir(exist_ok=True)
    console.print(f"Extracting frames from {video_path.name} at {fps:.2f} fps...")
    try:
        ffmpeg_cmd = [
            "ffmpeg",
            "-i",
            str(video_path),
            str(frames_dir / "frame_%06d.jpg"),
        ]
        subprocess.run(
            ffmpeg_cmd,
            capture_output=True,
            check=True,
            text=True,
        )
    except subprocess.CalledProcessError as e:
        console.print(f"[red]Error extracting frames for {video_path.name}: {e}[/red]")
        console.print(f"[red]FFMPEG stderr: {e.stderr}[/red]")
        return 0.0
    except FileNotFoundError:
        console.print("[red]ffmpeg command not found.[/red]")
        console.print("[yellow]Please ensure ffmpeg is installed and in your system's PATH.[/yellow]")
        return 0.0

    console.print(f"[green]Successfully extracted frames to {frames_dir}[/green]")
    return fps


def extract_audio(video_path: Path, audio_path: Path) -> bool:
    """Extracts audio from a video file and saves it as an MP3."""
    if audio_path.exists():
        console.print(f"Audio already exists for {video_path.name}, skipping.")
        return True

    console.print(f"Extracting audio from {video_path.name}...")
    try:
        ffmpeg_cmd = [
            "ffmpeg",
            "-i",
            str(video_path),
            "-vn",  # No video
            "-acodec",
            "libmp3lame",
            "-y",  # Overwrite output
            str(audio_path),
        ]
        subprocess.run(
            ffmpeg_cmd,
            capture_output=True,
            check=True,
            text=True,
        )
    except subprocess.CalledProcessError as e:
        console.print(f"[red]Error extracting audio for {video_path.name}: {e}[/red]")
        console.print(f"[red]FFMPEG stderr: {e.stderr}[/red]")
        if audio_path.exists():
            audio_path.unlink()
        return False
    except FileNotFoundError:
        console.print("[red]ffmpeg command not found.[/red]")
        console.print("[yellow]Please ensure ffmpeg is installed and in your system's PATH.[/yellow]")
        return False

    console.print(f"[green]Successfully extracted audio to {audio_path}[/green]")
    return True


def fix_youtube_transcript_timing(segments: list[TranscriptionSegment]) -> list[TranscriptionSegment]:
    """Fix overlapping end times in YouTube transcript segments.

    YouTube transcripts often have end times that extend past the start time of the next segment.
    This function adjusts end times to finish at or just before the start of the next segment.
    """
    if len(segments) <= 1:
        return segments

    fixed_segments = []
    for i, segment in enumerate(segments):
        if i < len(segments) - 1:  # Not the last segment
            next_segment = segments[i + 1]
            if segment.end > next_segment.start:
                # Adjust end time to be slightly before next segment starts
                adjusted_end = max(segment.start + 0.1, next_segment.start - 0.01)
                fixed_segment = TranscriptionSegment(
                    start=segment.start,
                    end=adjusted_end,
                    text=segment.text,
                )
                fixed_segments.append(fixed_segment)
            else:
                fixed_segments.append(segment)
        else:
            # Last segment, keep as is
            fixed_segments.append(segment)

    return fixed_segments


def download_youtube_transcript(video_url: str, video_title: str, output_path: Path) -> TranscriptionAnalysis | None:
    """Download YouTube's official transcript and convert to TranscriptionAnalysis format."""
    console.print(f"Downloading YouTube transcript for: {video_title}")

    # Create temporary directory for subtitle download
    temp_dir = output_path.parent / "temp_subtitles"
    temp_dir.mkdir(exist_ok=True)

    try:
        # yt-dlp options to download subtitles/captions
        ydl_opts = {
            "writesubtitles": True,
            "writeautomaticsub": True,  # Download auto-generated subtitles if manual ones aren't available
            "subtitleslangs": ["en"],  # English subtitles
            "subtitlesformat": "json3",  # JSON format for easier parsing
            "skip_download": True,  # Don't download the video, just the subtitles
            "outtmpl": str(temp_dir / "%(title)s.%(ext)s"),
            "quiet": True,  # Reduce output noise
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Download the subtitles
            ydl.download([video_url])

            # Find the downloaded subtitle file
            subtitle_files = list(temp_dir.glob("*.en.json3"))
            if not subtitle_files:
                console.print("[yellow]No English subtitles found for this video[/yellow]")
                return None

            subtitle_file = subtitle_files[0]

            # Load and parse the subtitle data
            with subtitle_file.open("r", encoding="utf-8") as f:
                subtitle_data = json.load(f)

            # Extract events (the actual transcript segments)
            events = subtitle_data.get("events", [])
            transcript_segments = []

            for event in events:
                # Skip events without text (like music notes)
                if "segs" not in event:
                    continue

                start_time = event.get("tStartMs", 0) / 1000.0  # Convert ms to seconds
                duration = event.get("dDurationMs", 0) / 1000.0
                end_time = start_time + duration

                # Combine all text segments in this event
                text_parts = []
                for seg in event["segs"]:
                    if "utf8" in seg:
                        text_parts.append(seg["utf8"])

                text = "".join(text_parts).strip()
                if text and not text.startswith("["):  # Skip music/sound effect markers
                    transcript_segments.append(
                        TranscriptionSegment(
                            start=start_time,
                            end=end_time,
                            text=text,
                        ),
                    )

            # Clean up temporary files
            subtitle_file.unlink()
            temp_dir.rmdir()

            if not transcript_segments:
                console.print("[yellow]No usable transcript segments found[/yellow]")
                return None

            # Fix overlapping timing issues in YouTube transcripts
            transcript_segments = fix_youtube_transcript_timing(transcript_segments)

            # Create TranscriptionAnalysis object
            transcription_result = TranscriptionAnalysis(
                language="en",  # YouTube transcripts are typically in the video's primary language
                title=video_title,
                segments=transcript_segments,
            )

            console.print(f"Downloaded YouTube transcript with {len(transcript_segments)} segments")
            console.print(f"Duration: {transcript_segments[0].start:.1f}s to {transcript_segments[-1].end:.1f}s")

            return transcription_result

    except Exception as e:
        console.print(f"[red]Error downloading YouTube transcript: {e}[/red]")
        # Clean up on error
        if temp_dir.exists():
            for file in temp_dir.glob("*"):
                file.unlink()
            temp_dir.rmdir()
        return None


def process_single_video(
    video: VideoInfo,
    video_dir: Path,
    transcription_pipeline: Any | None,
    run_transcription: bool,
    run_music_analysis: bool,
    run_sound_effects: bool = False,
    force_separation: bool = False,
    force_transcription: bool = False,
    force_music: bool = False,
    force_sfx: bool = False,
    force_timeline: bool = False,
    force_frames: bool = False,
    use_vad: bool = True,
    use_youtube_transcript: bool = True,
) -> None:
    """Processes a single video file that is already on disk."""
    video_start_time = time.time()
    video_id = video.id
    video_title = video.title or "Untitled Video"
    video_path = video_dir / f"{video_id}.mp4"

    # --- Path Definitions ---
    audio_path = video_dir / f"{video_id}.mp3"
    frames_dir = video_dir / "frames"
    separation_dir = video_dir / "separation"
    transcript_path = video_dir / "transcription.json"
    music_analysis_path = video_dir / "music.json"
    sound_effects_analysis_path = video_dir / "sound_effects_analysis.json"
    unique_frames_path = video_dir / "frames_unique.json"
    frame_descriptions_path = video_dir / "frame_descriptions.json"
    # Use video title for timeline filename (sanitized for filesystem)
    safe_title = "".join(c for c in video_title if c.isalnum() or c in (" ", "-", "_")).rstrip()
    safe_title = safe_title.replace(" ", "_")
    timeline_path = video_dir / f"{safe_title}_timeline.json"

    # --- Step 1: Extract Frames ---
    step_start_time = time.time()
    fps = extract_frames(video_path, frames_dir)
    if fps == 0.0:
        console.print(
            f"[red]Frame extraction failed for {video_path.name}. Aborting video processing.[/red]",
        )
        return

    log_step_duration(step_start_time, "Frame Extraction")

    # --- Step 1.5: Download YouTube Transcript (if enabled) ---
    transcription_result = None
    if run_transcription and use_youtube_transcript and video.url:
        step_start_time = time.time()
        console.print("Attempting to download YouTube transcript...")
        try:
            transcription_result = download_youtube_transcript(
                video_url=video.url,
                video_title=video_title,
                output_path=transcript_path,
            )

            if transcription_result:
                # Save the YouTube transcript in our format
                with transcript_path.open("w", encoding="utf-8") as f:
                    f.write(transcription_result.model_dump_json(indent=2))

                # Save metadata
                metadata_path = transcript_path.with_name("_transcription_metadata.json")
                save_idempotent_result(
                    output_path=metadata_path,
                    result=transcription_result,
                    input_files=[],  # YouTube transcript doesn't depend on local files
                    params={"source": "youtube", "video_url": video.url},
                )
                console.print("Successfully downloaded and saved YouTube transcript")
            else:
                console.print("[yellow]YouTube transcript not available, will try Whisper later[/yellow]")
        except Exception as e:
            console.print(f"[yellow]YouTube transcript download failed: {e}. Will try Whisper later[/yellow]")

        log_step_duration(step_start_time, "YouTube Transcript Download")

    # --- Step 2: Extract Full Audio ---
    step_start_time = time.time()
    audio_extracted = extract_audio(video_path, audio_path)
    log_step_duration(step_start_time, "Audio Extraction")
    if not audio_extracted:
        return

    # --- Step 3: Separate Speech and Music ---
    step_start_time = time.time()
    speech_path, music_path = separate_audio_demucs(
        audio_path,
        separation_dir,
        force=force_separation,
    )
    log_step_duration(step_start_time, "Audio Separation")
    if not speech_path or not music_path:
        console.print(
            "[bold red]Could not separate audio, aborting further analysis.[/bold red]",
        )
        return

    # --- Step 4: Transcribe Speech ---
    if run_transcription:
        step_start_time = time.time()

        # Fall back to Whisper transcription if YouTube transcript failed or not requested
        if not transcription_result and transcription_pipeline and speech_path:
            console.print("Analyzing transcription for vocals.wav...")
            try:
                transcription_result = analyze_transcription(
                    speech_path=speech_path,
                    output_path=transcript_path,
                    transcription_pipeline=transcription_pipeline,
                    video_title=video_title,
                    force=force_transcription,
                    use_vad=use_vad,
                )
            except Exception as e:
                console.print(f"[bold red]An unexpected error occurred during transcription: {e}[/bold red]")
                console.print(f"[bold red]Aborting further analysis for '{video_title}'.[/bold red]")
                return
        elif not transcription_result and not transcription_pipeline:
            console.print(
                "[yellow]Transcription enabled, but pipeline not available and YouTube transcript failed. Skipping.[/yellow]",
            )

        if not transcription_result:
            console.print(f"[bold red]Transcription failed for '{video_title}'. Aborting further analysis.[/bold red]")
            return

        log_step_duration(step_start_time, "Transcription Analysis")

    # --- Step 5: Analyze Music ---
    if run_music_analysis and music_path:
        analyze_music(
            audio_path=music_path,
            output_path=music_analysis_path,
            force=force_music,
        )

    # --- Step 6: Analyze Sound Effects ---
    if run_sound_effects and music_path:
        step_start_time = time.time()
        analyze_sound_effects(
            audio_path=music_path,
            output_path=sound_effects_analysis_path,
            force=force_sfx,
        )
        log_step_duration(step_start_time, "Sound Effects Analysis")

    # --- Step 7: Analyze Unique Frames ---
    frame_paths = sorted(frames_dir.glob("*.png"))  # Changed from *.jpg to *.png to match actual frame files
    if not frame_paths:
        console.print(f"[yellow]No frames found in {frames_dir}. Skipping frame analysis.[/yellow]")
    else:
        step_start_time = time.time()
        unique_frames_result = analyze_unique_frames(
            frame_paths=frame_paths,
            output_path=unique_frames_path,
            fps=fps,
            force=force_frames,
        )
        log_step_duration(step_start_time, "Unique Frame Analysis")

        # Step 8: Describe unique frames
        if unique_frames_result:
            step_start_time = time.time()
            analyze_frame_descriptions(
                unique_frames_path=unique_frames_path,
                output_path=frame_descriptions_path,
                force=force_frames,
            )
            log_step_duration(step_start_time, "Frame Description Analysis")

    # --- Step 9: Create Unified Timeline ---
    step_start_time = time.time()

    # Define the list of input files for the timeline.
    # This makes the timeline generation idempotent.
    timeline_input_files: list[Path] = []
    if run_transcription and transcript_path.exists():
        timeline_input_files.append(transcript_path)
    if run_music_analysis and music_analysis_path.exists():
        timeline_input_files.append(music_analysis_path)
    if run_sound_effects and sound_effects_analysis_path.exists():
        timeline_input_files.append(sound_effects_analysis_path)
    if frame_descriptions_path.exists():
        timeline_input_files.append(frame_descriptions_path)

    # Use a separate file for the idempotency check of the timeline generation step
    timeline_metadata_path = video_dir / "_timeline_metadata.json"

    # Try to load the timeline from cache
    cached_timeline_wrapper = load_and_check_idempotency(
        output_path=timeline_metadata_path,
        model_class=Timeline,
        input_files=timeline_input_files,
        force=force_timeline,
    )

    if cached_timeline_wrapper:
        console.print(f"Unified timeline is up to date for [cyan]{video_title}[/cyan].")
        # Ensure the final file exists, even if we skip generation
        if not timeline_path.exists():
            console.print("[yellow]Cache hit, but output file is missing. Regenerating.[/yellow]")
            # Fall through to regenerate if file is missing
        else:
            # If everything is up to date and file exists, we can stop.
            log_step_duration(step_start_time, "Timeline Generation")
            log_step_duration(video_start_time, f"Total time for '{video_title}'")
            return
    else:
        console.print(f"Creating unified timeline for [cyan]{video_title}[/cyan]...")
        # --- Timeline Generation ---
        timeline_events: list[TimelineEvent] = []

        # Add transcript events
        if run_transcription and transcript_path.exists():
            # Load transcription from clean format (no idempotency wrapper)
            try:
                with transcript_path.open("r", encoding="utf-8") as f:
                    transcript_data = json.load(f)
                transcription_result = TranscriptionAnalysis.model_validate(transcript_data)
            except Exception as e:
                console.print(f"[yellow]Failed to load transcription: {e}[/yellow]")
                transcription_result = None
            if transcription_result:
                timeline_events.extend(
                    [
                        TimelineEvent(
                            type="transcript",
                            start_time=segment.start,
                            end_time=segment.end,
                            text=segment.text,
                        )
                        for segment in transcription_result.segments
                    ],
                )

        # Add music events
        if run_music_analysis and music_analysis_path.exists():
            music_result = load_and_check_idempotency(
                music_analysis_path,
                MusicAnalysis,
            )
            if music_result:
                timeline_events.extend(
                    [
                        TimelineEvent(
                            type="music",
                            start_time=event.start,
                            end_time=event.end,
                            text=event.label,
                        )
                        for event in music_result.events
                    ],
                )

        # Add sound effect events
        if run_sound_effects and sound_effects_analysis_path.exists():
            with sound_effects_analysis_path.open("r", encoding="utf-8") as f:
                sound_effects_data = json.load(f).get("result", {})
                timeline_events.extend(
                    [
                        TimelineEvent(
                            type="sound_effect",
                            start_time=event["start"],
                            end_time=event["end"],
                            text=event["label"],
                        )
                        for event in sound_effects_data.get("events", [])
                    ],
                )

        # Add frame analysis events
        if frame_descriptions_path.exists():
            with frame_descriptions_path.open("r", encoding="utf-8") as f:
                frames_data = json.load(f).get("result", {})
                timeline_events.extend(
                    [
                        TimelineEvent(
                            type="visual_description",
                            start_time=frame["timestamp"],
                            end_time=frame["timestamp"],
                            text=frame["description"],
                            image_path=frame["frame_path"],
                        )
                        for frame in frames_data.get("described_frames", [])
                    ],
                )

        # Sort events by start time
        timeline_events.sort(key=lambda x: x.start_time)

        console.print(f"DEBUG: Checking video metadata: {video!r}")
        if not video.id or not video.url:
            console.print("[bold red]Video ID or URL is missing, cannot generate timeline metadata.[/bold red]")
            # This should not happen if youtube download was successful
            return

        # Create final Pydantic model
        final_timeline = Timeline(
            video_metadata=VideoMetadataTimeline(
                title=video.title,
                author=video.author,
                url=video.url,
                id=video.id,
                view_count=video.view_count,
                upload_date=video.upload_date,
            ),
            analysis_timeline=timeline_events,
        )

        # Save the final results
        # 1. The clean timeline events array
        with timeline_path.open("w", encoding="utf-8") as f:
            json.dump([event.model_dump() for event in final_timeline.analysis_timeline], f, indent=2)

        # 2. The idempotency wrapper for this step
        save_idempotent_result(
            output_path=timeline_metadata_path,
            result=final_timeline,  # Save the whole timeline object in the wrapper
            input_files=timeline_input_files,
        )
        console.print(f"Unified timeline saved to [green]{timeline_path.name}[/green]")

    log_step_duration(step_start_time, "Timeline Generation")

    # --- End of video processing ---
    log_step_duration(video_start_time, f"Total time for '{video_title}'")


def process_videos(
    videos: list[VideoInfo],
    output_dir: Path,
    transcription_pipeline: Any | None,
    run_transcription: bool,
    run_music_analysis: bool,
    run_sound_effects: bool = False,
    force_separation: bool = False,
    force_transcription: bool = False,
    force_music: bool = False,
    force_sfx: bool = False,
    force_timeline: bool = False,
    force_frames: bool = False,
    use_vad: bool = True,
    use_youtube_transcript: bool = True,
) -> None:
    """Downloads videos and processes them."""
    console.print(f"Processing {len(videos)} videos in {output_dir}...")
    if not output_dir.exists():
        output_dir.mkdir(parents=True)

    for i, video in enumerate(videos):
        console.print(
            f"[bold cyan]--- Starting video {i + 1}/{len(videos)}: {video.title} ---[/bold cyan]",
        )

        if not video.id or not video.title:
            console.print("[yellow]Skipping video with missing ID or title.[/yellow]")
            continue

        safe_title = re.sub(r'[\/*?"<>|]', "", video.title)
        video_dir = output_dir / safe_title
        video_dir.mkdir(exist_ok=True)

        # --- Path Definitions ---
        video_path = video_dir / f"{video.id}.mp4"

        # --- Step 1: Download Video ---
        step_start_time = time.time()
        download_succeeded = False
        if not video_path.exists() and video.url:
            console.print(f"Downloading '{video.title}'...")
            ydl_opts: dict[str, Any] = {
                "format": "bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best",
                "outtmpl": str(video_path),
                "quiet": True,
            }
            try:
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    ydl.download([video.url])
                download_succeeded = True
            except yt_dlp.utils.DownloadError as e:
                console.print(f"[red]Error downloading {video.title}: {e}[/red]")
                continue
        elif video_path.exists():
            console.print(f"Video '{video.title}' already exists. Skipping download.")
            download_succeeded = True
        else:
            console.print(
                f"[yellow]Skipping download of '{video.title}' because it has no URL.[/yellow]",
            )
            continue
        log_step_duration(step_start_time, "Video Download")

        if not download_succeeded:
            continue

        # --- Process the downloaded video ---
        if download_succeeded or video_path.exists():
            log_step_duration(step_start_time, f"Download '{video.title}'")
            process_single_video(
                video=video,
                video_dir=video_dir,
                transcription_pipeline=transcription_pipeline,
                run_transcription=run_transcription,
                run_music_analysis=run_music_analysis,
                run_sound_effects=run_sound_effects,
                force_separation=force_separation,
                force_transcription=force_transcription,
                force_music=force_music,
                force_sfx=force_sfx,
                force_timeline=force_timeline,
                force_frames=force_frames,
                use_vad=use_vad,
                use_youtube_transcript=use_youtube_transcript,
            )

        console.print(
            f"[bold green]--- Finished video {i + 1}/{len(videos)} ---[/bold green]\n",
        )
